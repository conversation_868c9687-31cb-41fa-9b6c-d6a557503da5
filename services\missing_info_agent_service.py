import asyncio
import logging
from collections import defaultdict
from datetime import datetime, timezone
from typing import TypedDict, List, Dict, Any

from langgraph.graph import StateGraph, START, END

from db import get_db
from db.monitoring import update_agent_run_timestamp
from db.missing_info_agent_operations import (
    get_all_referrals_with_missing_fields,
    get_all_referrals_with_new_attachments,
    get_clients,
    get_empty_data_points,
    insert_referral_extracted_fields,
    update_referral_extracted_fields,
    update_task_creation_status,
)
from db.task_and_workflow_agent_operations import (
    create_completed_task_for_visibility,
)
from services.pdfplus_service import call_pdfplus_extract_fields_api
from utils.logging_config import REQUEST_ID_VAR


class MissingInfoAgentState(TypedDict):
    """Represents the data flowing through our pipeline."""

    patient_referrals: List[Dict[str, Any]]
    new_referrals_api_results: List[Dict[str, Any]]
    agent_summary: List[Dict[str, Any]]
    referral_tracking: List[Dict[str, Any]]
    client_schema: str
    request_id: str

    retriggering_referrals: List[Dict[str, Any]]
    new_attachments_api_results: List[Dict[str, Any]]
    update_db_summary: List[Dict[str, Any]]
    retrigger_agent_summary: List[Dict[str, Any]]


class MissingInfoAgent:

    def __init__(self):
        self.graph = StateGraph(MissingInfoAgentState)

        self.graph.add_node(
            "Identify Referrals with Missing Fields",
            self.identify_referrals_with_missing_fields,
        )
        self.graph.add_node(
            "Merge OCR Data for New Referrals", self.merge_ocr_data_for_new_referrals
        )
        self.graph.add_node(
            "Extract Missing Fields Data for New Referrals",
            self.extract_missing_fields_for_new_referrals,
        )
        self.graph.add_node("Update the fields in DB", self.update_db_for_new_referrals)
        self.graph.add_node(
            "Summarize Missing Info Agent", self.summarize_agent_actions
        )

        # Branch for new referrals
        self.graph.add_edge(START, "Identify Referrals with Missing Fields")
        self.graph.add_edge(
            "Identify Referrals with Missing Fields", "Merge OCR Data for New Referrals"
        )
        self.graph.add_edge(
            "Merge OCR Data for New Referrals",
            "Extract Missing Fields Data for New Referrals",
        )
        self.graph.add_edge(
            "Extract Missing Fields Data for New Referrals", "Update the fields in DB"
        )
        self.graph.add_edge("Update the fields in DB", "Summarize Missing Info Agent")
        self.graph.add_edge("Summarize Missing Info Agent", END)

        # Branch for retriggering referrals
        self.graph.add_node(
            "Identify Referrals with New Attachments",
            self.identify_referrals_with_new_attachments,
        )
        self.graph.add_node(
            "Merge OCR Data for New Attachments",
            self.merge_ocr_data_for_new_attachments,
        )
        self.graph.add_node(
            "Get Empty Data Points to Retrigger",
            self.get_missing_data_points_to_retrigger,
        )
        self.graph.add_node(
            "Extract Missing Fields Data for New Attachments",
            self.extract_missing_fields_for_new_attachments,
        )
        self.graph.add_node(
            "Update the retrigger fields in DB",
            self.update_extracted_fields_for_new_attachments,
        )
        self.graph.add_node(
            "Update Task Creation Status",
            self.update_task_creation_status_if_data_filled_with_retrigger,
        )
        self.graph.add_node(
            "Summarize Retrigger Agent", self.summarize_retrigger_agent_actions
        )

        self.graph.add_edge(START, "Identify Referrals with New Attachments")
        self.graph.add_edge(
            "Identify Referrals with New Attachments",
            "Merge OCR Data for New Attachments",
        )
        self.graph.add_edge(
            "Merge OCR Data for New Attachments", "Get Empty Data Points to Retrigger"
        )
        self.graph.add_edge(
            "Get Empty Data Points to Retrigger",
            "Extract Missing Fields Data for New Attachments",
        )
        self.graph.add_edge(
            "Extract Missing Fields Data for New Attachments",
            "Update the retrigger fields in DB",
        )
        self.graph.add_edge(
            "Update the retrigger fields in DB", "Update Task Creation Status"
        )
        self.graph.add_edge("Update Task Creation Status", "Summarize Retrigger Agent")
        self.graph.add_edge("Summarize Retrigger Agent", END)

    async def execute_workflow(self, state: MissingInfoAgentState):
        """Executes the workflow for a given client schema."""
        return await self.graph.compile(debug=False).ainvoke(state)

    async def run(self, clients: List[str]):
        logging.info(f"Identified {len(clients)} client(s).")
        tasks = []
        for client in clients:
            state = MissingInfoAgentState(
                patient_referrals=[],
                new_referrals_api_results=[],
                agent_summary=[],
                referral_tracking=[],
                retriggering_referrals=[],
                new_attachments_api_results=[],
                client_schema=f"{client}_referral",
                request_id=REQUEST_ID_VAR.get(),
            )
            tasks.append(self.execute_workflow(state))

        await asyncio.gather(*tasks)
        logging.info("Workflow completed.")

    async def identify_referrals_with_missing_fields(
        self, state: MissingInfoAgentState
    ) -> MissingInfoAgentState:
        """
        Query the database for referrals missing fields. Store those in state["patient_referrals"].
        Args:
            state: The current state of the agent.
        Returns:
            state: The updated state with the missing referrals.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Querying PostgreSQL for records missing fields..")
        referrals_with_missing_fields = []
        async with get_db() as db:
            missing_fields_results = await get_all_referrals_with_missing_fields(
                state["client_schema"], db
            )
            if missing_fields_results:
                for referral in missing_fields_results:
                    missing_fields = [
                        field
                        for field, value in referral.items()
                        if field
                        not in ["refer_id", "ocr_data", "patient_id", "filecontent_id"]
                        and (value is None or not value.strip())
                    ]
                    referral["missing_data_points"] = missing_fields
                    referrals_with_missing_fields.append(referral)

                logging.info(
                    f"Found {len(referrals_with_missing_fields)} record(s) with missing fields."
                )
        return {"patient_referrals": referrals_with_missing_fields}

    async def merge_ocr_data_for_new_referrals(
        self, state: MissingInfoAgentState
    ) -> MissingInfoAgentState:
        """
        Merge the multiple OCR Data attachments for referral, store them in state["patient_referrals"].
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Merging the OCR data for new referrals.")
        patient_referrals = state.get("patient_referrals", [])
        merged_ocr_data = await self._merge_ocr_data(patient_referrals)
        return {"patient_referrals": merged_ocr_data}

    async def _merge_ocr_data(
        self, referrals: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        grouped_referral = defaultdict(list)
        merged_ocr_data = []
        if referrals:
            for referral_record in referrals:
                grouped_referral[referral_record["refer_id"]].append(referral_record)
            for refer_id, entries in grouped_referral.items():
                merged_ocr = {}
                merged_referral = {"refer_id": refer_id, "filecontent_ids": []}
                current_page = 1
                for entry in entries:
                    # Filter out non-numeric keys and handle only page numbers
                    numeric_keys = []
                    for key in entry["ocr_data"]:
                        try:
                            numeric_keys.append(int(key))
                        except ValueError:
                            # Skip non-numeric keys like 'usageCost'
                            continue
                    
                    # Sort the numeric keys and process them
                    for page_number in sorted(numeric_keys):
                        merged_ocr[str(current_page)] = entry["ocr_data"][str(page_number)]
                        current_page += 1
                        if (
                            entry["filecontent_id"]
                            not in merged_referral["filecontent_ids"]
                        ):
                            merged_referral["filecontent_ids"].append(
                                entry["filecontent_id"]
                            )

                for key in entry.keys():
                    if key not in ["refer_id", "ocr_data", "filecontent_id"]:
                        merged_referral[key] = entry[key]
                merged_referral["ocr_data"] = merged_ocr
                merged_ocr_data.append(merged_referral)
        return merged_ocr_data

    async def extract_missing_fields_for_new_referrals(
        self, state: MissingInfoAgentState
    ) -> MissingInfoAgentState:
        """
        For each Referral, identify missing fields and call the PDFPlus API to extract it in parallel.
        Store results in state["new_referrals_api_results"].
        Args:
            state: The current state of the agent.
        Returns:
            state: The updated state with the API results.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Extracting the missing fields from OCR attachments.")
        patient_referrals = state.get("patient_referrals", [])
        if not patient_referrals:
            return {}
        pdfplus_api_results = await self._extract_missing_fields_data(
            patient_referrals, state["request_id"]
        )
        return {"new_referrals_api_results": pdfplus_api_results}

    async def _extract_missing_fields_data(
        self, referrals: List[Dict[str, Any]], request_id: str
    ) -> List[Dict[str, Any]]:
        pdfplus_api_results = []

        async def process_referral(referral):
            REQUEST_ID_VAR.set(request_id)
            refer_id = referral["refer_id"]
            ocr_data = referral["ocr_data"]
            patient_id = referral["patient_id"]
            missing_fields = referral["missing_data_points"]
            if missing_fields:
                pdfplus_response = await call_pdfplus_extract_fields_api(
                    str(refer_id), ocr_data, missing_fields
                )
                return {
                    "refer_id": refer_id,
                    "patient_id": patient_id,
                    "missing_fields": missing_fields,
                    "api_data_json": pdfplus_response,
                }
            return {}

        tasks = [process_referral(referral) for referral in referrals]
        pdfplus_results = await asyncio.gather(*tasks)

        for pdfplus_result in pdfplus_results:
            if pdfplus_result:
                pdfplus_api_results.append(pdfplus_result)
        return pdfplus_api_results

    async def update_db_for_new_referrals(
        self, state: MissingInfoAgentState
    ) -> MissingInfoAgentState:
        """
        For each item in state["new_referrals_api_results"], attempt to update the DB if we have new data.
        Summaries go to state["agent_summary"].
        Args:
            state: The current state of the agent.
        Returns:
            state: The updated state with the DB update summary.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Updating the DB with extracted fields.")
        api_results = state.get("new_referrals_api_results", [])
        agent_summary = []
        async with get_db() as db:
            for item in api_results:
                refer_id = item["refer_id"]
                patient_id = item["patient_id"]
                api_data_json = item["api_data_json"]

                filled_fields = [
                    field
                    for field, value in api_data_json.items()
                    if value is not None and value != ""
                ]

                has_missing_values = any(
                    v is None or v == "" for v in api_data_json.values()
                )

                update_info = await insert_referral_extracted_fields(
                    refer_id=refer_id,
                    patient_id=patient_id,
                    api_data_json=api_data_json,
                    schema_name=state["client_schema"],
                    require_task=has_missing_values,
                    db=db,
                )
                agent_summary.append(update_info)

                if not has_missing_values and filled_fields:
                    completed_task_info = await create_completed_task_for_visibility(
                        db=db,
                        refer_id=refer_id,
                        patient_id=patient_id,
                        filled_fields=filled_fields,
                        schema_name=state["client_schema"],
                    )
                    completed_task_info["type"] = "COMPLETED_TASK_FOR_VISIBILITY"
                    agent_summary.append(completed_task_info)
                await db.commit()

        return {"agent_summary": agent_summary}

    async def summarize_agent_actions(self, state: MissingInfoAgentState) -> str:
        """
        End node: returns a textual summary of what happened.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Summarizing the Missing Info Agent.")

        completed_tasks_count = len(
            [
                summary
                for summary in state.get("agent_summary", [])
                if summary.get("type") == "COMPLETED_TASK_FOR_VISIBILITY"
            ]
        )

        summary_lines = [
            "\n=== Missing Info Agent Summary ===",
            f"Referrals with Missing Values: {len(state.get('patient_referrals', []))}",
            f"API Calls to get Missing Values: {len(state.get('new_referrals_api_results', []))}",
            f"Completed Tasks Created for Visibility: {completed_tasks_count}",
        ]
        summary_lines.append("Update DB Results:")
        for db_update_result in state.get("agent_summary", []):
            if db_update_result.get("type") == "COMPLETED_TASK_FOR_VISIBILITY":
                summary_lines.append(
                    f" - refer_id={db_update_result['refer_id']} status={db_update_result['status']} "
                    f"msg=Completed task created for visibility (fields: {', '.join(db_update_result.get('filled_fields', []))})"
                )
            else:
                summary_lines.append(
                    f" - refer_id={db_update_result['refer_id']} status={db_update_result['status']} msg={db_update_result['message']}"
                )
        summary_lines.append("=======================================")
        summary_lines = "\n".join(summary_lines)
        logging.info(summary_lines)

    # Branch for retriggering referrals
    async def identify_referrals_with_new_attachments(
        self, state: MissingInfoAgentState
    ) -> MissingInfoAgentState:
        """
        Query the database for referrals with new attachments.
        Args:
            state: The current state of the agent.
        Returns:
            state: The updated state with the new attachments.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info(
            "Retrigger: Querying PostgreSQL for records with new attachments.."
        )
        referrals_with_new_attachments = []
        async with get_db() as db:
            referrals_with_new_attachments = (
                await get_all_referrals_with_new_attachments(state["client_schema"], db)
            )
        logging.info(
            f"Found {len(referrals_with_new_attachments)} record(s) with new attachments."
        )
        return {"retriggering_referrals": referrals_with_new_attachments}

    async def merge_ocr_data_for_new_attachments(
        self, state: MissingInfoAgentState
    ) -> MissingInfoAgentState:
        """
        Merge the multiple OCR Data attachments for referral, store them in state["retriggering_referrals"].
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Retrigger: Merging the OCR data for new attachments.")
        retriggering_referrals = state.get("retriggering_referrals", [])
        merged_ocr_data = await self._merge_ocr_data(retriggering_referrals)
        return {"retriggering_referrals": merged_ocr_data}

    async def get_missing_data_points_to_retrigger(
        self, state: MissingInfoAgentState
    ) -> List[str]:
        """
        Get the missing data points to retrigger for a given client schema.
        Args:
            state: The current state of the agent.
        Returns:
            List[str]: The list of missing data points to retrigger.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Retrigger: Getting missing data points to retrigger.")
        retriggering_referrals = []
        async with get_db() as db:
            for new_attachment in state.get("retriggering_referrals", []):
                empty_data_points = await get_empty_data_points(
                    new_attachment["refer_id"], state["client_schema"], db
                )
                if empty_data_points:
                    new_attachment["missing_data_points"] = empty_data_points
                    retriggering_referrals.append(new_attachment)
        return {"retriggering_referrals": retriggering_referrals}

    async def extract_missing_fields_for_new_attachments(
        self, state: MissingInfoAgentState
    ) -> MissingInfoAgentState:
        """
        For each Referral, identify missing fields and call the PDFPlus API to extract it in parallel.
        Args:
            state: The current state of the agent.
        Returns:
            state: The updated state with the API results.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Retrigger: Extracting the missing fields from OCR attachments.")
        retriggering_referrals = state.get("retriggering_referrals", [])
        if not retriggering_referrals:
            return {}
        api_results = await self._extract_missing_fields_data(
            retriggering_referrals, state["request_id"]
        )
        return {"new_attachments_api_results": api_results}

    async def update_extracted_fields_for_new_attachments(
        self, state: MissingInfoAgentState
    ) -> MissingInfoAgentState:
        """
        For each item in state["new_referrals_api_results"], attempt to update the DB if we have new data.
        Summaries go to state["agent_summary"].
        Args:
            state: The current state of the agent.
        Returns:
            state: The updated state with the DB update summary.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Updating the DB with extracted fields.")
        api_results = state.get("new_attachments_api_results", [])
        if not api_results:
            return {}
        update_db_summary = []
        async with get_db() as db:
            for item in api_results:
                refer_id = item["refer_id"]
                api_data_json = {
                    k: v
                    for k, v in item["api_data_json"].items()
                    if v is not None and v != ""
                }
                if api_data_json:
                    update_info = await update_referral_extracted_fields(
                        refer_id, api_data_json, state["client_schema"], db
                    )
                    update_db_summary.append(update_info)
        return {"update_db_summary": update_db_summary}

    async def update_task_creation_status_if_data_filled_with_retrigger(
        self, state: MissingInfoAgentState
    ) -> MissingInfoAgentState:
        """
        Update the task creation status if no more missing values after retrigger.
        Args:
            state: The current state of the agent.
        Returns:
            state: The updated state.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Updating the task creation status.")
        update_db_summary = state.get("update_db_summary", [])
        if not update_db_summary:
            return {}
        retrigger_agent_summary = []
        async with get_db() as db:
            for item in update_db_summary:
                refer_id = item["refer_id"]
                update_status = await update_task_creation_status(
                    refer_id=refer_id, schema_name=state["client_schema"], db=db
                )
                retrigger_agent_summary.append(update_status)
        return {"retrigger_agent_summary": retrigger_agent_summary}

    async def summarize_retrigger_agent_actions(
        self, state: MissingInfoAgentState
    ) -> str:
        """
        End node: returns a textual summary of what happened.
        """
        REQUEST_ID_VAR.set(state["request_id"])
        logging.info("Summarizing the Retrigger Agent.")
        summary_lines = [
            "\n=== Retrigger Agent Summary ===",
            f"Referrals with New Attachments: {len(state.get('retriggering_referrals', []))}",
            f"API Calls to get Missing Values: {len(state.get('new_attachments_api_results', []))}",
            f"Updated Task Statuses: {len([summary for summary in state.get('retrigger_agent_summary',[]) if summary['status']=='UPDATED_REQUIRE_TASK_FALSE'])}",
        ]
        summary_lines.append("Update DB Results:")
        for db_update_result in state.get("update_db_summary", []):
            summary_lines.append(
                f" - refer_id={db_update_result['refer_id']} status={db_update_result['status']} msg={db_update_result['message']}"
            )
        summary_lines.append("=======================================")
        summary_lines = "\n".join(summary_lines)
        logging.info(summary_lines)

    async def start(self, user="Scheduler"):
        logging.info(f"Missing Info Agent started by {user}.")
        clients = await get_clients()
        current_time = datetime.now(timezone.utc)
        await self.run(clients)
        async with get_db() as db:
            for client in clients:
                schema_name = f"{client}_referral"
                # Update Missing Info Agent timestamp
                await update_agent_run_timestamp(
                    db=db,
                    agent_name="Missing Info Agent",
                    schema_name=schema_name,
                    run_timestamp=current_time,
                )
                # Update Retrigger Agent timestamp
                await update_agent_run_timestamp(
                    db=db,
                    agent_name="Retrigger Agent",
                    schema_name=schema_name,
                    run_timestamp=current_time,
                )

        return {"message": f"Missing Info Agent completed successfully by {user}."}
